RewriteEngine On

# Admin blog URL rewrite (before general rules)
RewriteRule ^admin/blog/?$ admin/blog.php [L]

# Remove .php extension from URLs
RewriteCond %{THE_REQUEST} \s/([^.]+)\.php [NC]
RewriteRule ^ /%1 [R=301,L]

# Internally add .php extension to files
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME}.php -f
RewriteRule ^(.*)$ $1.php [L]


# Ensure the blog URL is routed to the correct file
# RewriteCond %{REQUEST_FILENAME} !-f
# RewriteCond %{REQUEST_FILENAME} !-d
# RewriteRule ^blog/(.*)$ blog.php?url=$1 [QSA,L]

# blog details
# გადაამისამართებს /blog/slug -> /blog_details.php?url=slug
RewriteRule ^blog/([a-zA-Z0-9-]+)$ blog_details.php?url=$1 [L]

#ბლოგების სიის გვერდიც იყოს /blog-ზე
RewriteRule ^blog/?$ blog.php [L]

RewriteRule ^blog/public/(.*)$ /public/$1 [L,NC,R=301]

RewriteCond %{THE_REQUEST} /blog/blog [NC]
RewriteRule ^ /blog [L,R=301]

RewriteRule ^blog/?$ blog.php [L]

#
Access-Control-Allow-Origin: https://safecarhauler.com
Access-Control-Allow-Methods: POST, OPTIONS
Access-Control-Allow-Headers: Content-Type, AuthKey